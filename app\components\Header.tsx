import { Link, useLocation } from "react-router";

export default function Header() {
  const location = useLocation();
  
  const isActive = (path: string) => {
    return location.pathname === path || 
           (path !== "/" && location.pathname.startsWith(path));
  };

  return (
    <header className="bg-blue-900 text-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="text-2xl font-bold">Robotica Arad</Link>
          
          <nav className="hidden md:block">
            <ul className="flex space-x-6">
              <li>
                <Link 
                  to="/" 
                  className={`hover:text-blue-300 transition ${isActive("/") ? "font-bold" : ""}`}
                >
                  Acasă
                </Link>
              </li>
              <li>
                <Link 
                  to="/robotica" 
                  className={`hover:text-blue-300 transition ${isActive("/robotica") ? "font-bold" : ""}`}
                >
                  Robotica
                </Link>
              </li>
              <li className="relative group">
                <Link 
                  to="/echipe" 
                  className={`hover:text-blue-300 transition ${isActive("/echipe") ? "font-bold" : ""}`}
                >
                  Echipe
                </Link>
                <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                  <Link 
                    to="/echipe/infinity-bolts" 
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Infinity Bolts
                  </Link>
                  <Link 
                    to="/echipe/harambe-cartel" 
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Harambe Cartel
                  </Link>
                  <Link 
                    to="/echipe/roboschool" 
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    RoboSchool
                  </Link>
                </div>
              </li>
              <li>
                <Link 
                  to="/evenimente" 
                  className={`hover:text-blue-300 transition ${isActive("/evenimente") ? "font-bold" : ""}`}
                >
                  Evenimente
                </Link>
              </li>
              <li>
                <Link 
                  to="/blog" 
                  className={`hover:text-blue-300 transition ${isActive("/blog") ? "font-bold" : ""}`}
                >
                  Blog
                </Link>
              </li>
            </ul>
          </nav>
          
          <button className="md:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
}