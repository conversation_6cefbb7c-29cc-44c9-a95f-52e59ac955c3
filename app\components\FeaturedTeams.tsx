import { Link } from "react-router";

interface Team {
  id: string;
  name: string;
  description: string;
  image: string;
  achievements: string[];
  route: string;
}

const teams: Team[] = [
  {
    id: "infinity-bolts",
    name: "<PERSON> Bolts",
    description: "Echipa noastră de robotică FTC cu experiență în competiții naționale și internaționale.",
    image: "/images/teams/infinity-bolts.jpg",
    achievements: ["Câștigători Regionale FTC 2023", "Premiul pentru Design", "Calificați la Naționale"],
    route: "/echipe/infinity-bolts"
  },
  {
    id: "harambe-cartel",
    name: "Harambe Cartel",
    description: "O echipă tânără și ambițioasă, specializată în programare avansată și design inovator.",
    image: "/images/teams/harambe-cartel.jpg",
    achievements: ["Premiul pentru Programare", "Locul 2 la Regionale", "Cel mai bun rookie team"],
    route: "/echipe/harambe-cartel"
  },
  {
    id: "roboschool",
    name: "RoboSchool",
    description: "Echipa educațională care se concentrează pe învățarea și dezvoltarea tinerilor roboticieni.",
    image: "/images/teams/roboschool.jpg",
    achievements: ["Premiul pentru Educație", "Cel mai bun mentor", "Proiect comunitar"],
    route: "/echipe/roboschool"
  }
];

export default function FeaturedTeams() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {teams.map((team) => (
        <div key={team.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
          <div className="h-48 bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
            <div className="text-white text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold">{team.name}</h3>
            </div>
          </div>
          
          <div className="p-6">
            <p className="text-gray-600 mb-4">{team.description}</p>
            
            <div className="mb-4">
              <h4 className="font-semibold text-gray-800 mb-2">Realizări:</h4>
              <ul className="space-y-1">
                {team.achievements.map((achievement, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {achievement}
                  </li>
                ))}
              </ul>
            </div>
            
            <Link 
              to={team.route}
              className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
            >
              Află mai multe
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
}
