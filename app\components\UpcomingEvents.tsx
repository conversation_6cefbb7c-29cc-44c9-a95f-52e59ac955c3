import { Link } from "react-router";

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  type: "competition" | "workshop" | "meeting" | "demo";
  registrationRequired: boolean;
}

const events: Event[] = [
  {
    id: "ftc-regionale-2024",
    title: "Competiția FTC Regionale 2024",
    date: "15 Martie 2024",
    time: "09:00 - 18:00",
    location: "Colegiul Național Moise Nicoară, Arad",
    description: "Competiția regională First Tech Challenge unde echipele noastre vor concura pentru calificarea la naționale.",
    type: "competition",
    registrationRequired: true
  },
  {
    id: "workshop-programare",
    title: "Workshop Programare Arduino",
    date: "22 Martie 2024",
    time: "14:00 - 17:00",
    location: "Centrul de Robotică Arad",
    description: "Workshop practic pentru începători despre programarea microcontrollerelor Arduino și senzorilor.",
    type: "workshop",
    registrationRequired: true
  },
  {
    id: "demo-robotica",
    title: "Demonstrație Publică de Robotică",
    date: "5 Aprilie 2024",
    time: "10:00 - 16:00",
    location: "Atrium Mall Arad",
    description: "Prezentare publică a roboților noștri și activități interactive pentru copii și tineri.",
    type: "demo",
    registrationRequired: false
  },
  {
    id: "intalnire-echipe",
    title: "Întâlnirea Lunară a Echipelor",
    date: "12 Aprilie 2024",
    time: "18:00 - 20:00",
    location: "Centrul de Robotică Arad",
    description: "Întâlnire de coordonare între echipe, planificare proiecte și schimb de experiențe.",
    type: "meeting",
    registrationRequired: false
  }
];

const getEventTypeColor = (type: Event["type"]) => {
  switch (type) {
    case "competition":
      return "bg-red-100 text-red-800";
    case "workshop":
      return "bg-blue-100 text-blue-800";
    case "meeting":
      return "bg-green-100 text-green-800";
    case "demo":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getEventTypeLabel = (type: Event["type"]) => {
  switch (type) {
    case "competition":
      return "Competiție";
    case "workshop":
      return "Workshop";
    case "meeting":
      return "Întâlnire";
    case "demo":
      return "Demonstrație";
    default:
      return "Eveniment";
  }
};

export default function UpcomingEvents() {
  return (
    <div className="space-y-6">
      {events.map((event) => (
        <div key={event.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition duration-300">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-xl font-bold text-gray-800">{event.title}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(event.type)}`}>
                  {getEventTypeLabel(event.type)}
                </span>
              </div>
              
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 text-gray-600 mb-3">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  {event.date}
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  {event.time}
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {event.location}
                </div>
              </div>
              
              <p className="text-gray-700 mb-4">{event.description}</p>
              
              {event.registrationRequired && (
                <div className="flex items-center text-orange-600 mb-4">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Înregistrare necesară
                </div>
              )}
            </div>
            
            <div className="mt-4 md:mt-0 md:ml-6">
              <Link 
                to="/evenimente"
                className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
              >
                Detalii
              </Link>
            </div>
          </div>
        </div>
      ))}
      
      <div className="text-center mt-8">
        <Link 
          to="/evenimente"
          className="inline-block bg-gray-100 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-200 transition duration-300"
        >
          Vezi toate evenimentele
        </Link>
      </div>
    </div>
  );
}
