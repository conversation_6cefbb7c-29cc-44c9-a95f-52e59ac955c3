import { Link } from "react-router";

export default function HeroSection() {
  return (
    <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20 relative">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Bun venit la Robotica Arad
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100">
            Comunitatea care dezvoltă pasiunea pentru robotică și tehnologie în Arad
          </p>
          <p className="text-lg mb-10 text-blue-200 max-w-2xl mx-auto">
            Alătură-te echipelor noastre de robotică și descoperă lumea fascinantă a tehnologiei, 
            programării și inovației. Împreună construim viitorul!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/echipe" 
              className="bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition duration-300 transform hover:scale-105"
            >
              Descoperă Echipele
            </Link>
            <Link 
              to="/evenimente" 
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition duration-300"
            >
              Vezi Evenimente
            </Link>
          </div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-400 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-blue-300 rounded-full opacity-30 animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-blue-500 rounded-full opacity-25 animate-pulse delay-500"></div>
      </div>
    </section>
  );
}
