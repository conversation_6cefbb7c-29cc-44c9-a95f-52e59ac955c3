import type { Route } from "../+types/home";
import { Link } from "react-router";
import HeroSection from "../components/HeroSection";
import FeaturedTeams from "../components/FeaturedTeams";
import UpcomingEvents from "../components/UpcomingEvents";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Robotica Arad - Acasă" },
    { name: "description", content: "Comunitatea de robotică din Arad" },
  ];
}

export default function Home() {
  return (
    <div>
      <HeroSection />
      
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Despre Noi</h2>
          <div className="max-w-3xl mx-auto text-lg text-gray-700">
            <p className="mb-6">
              Robotica Arad este o comunitate dedicată promovării ș<PERSON>, tehnologiei și roboticii în rândul tinerilor din Arad.
            </p>
            <p className="mb-6">
              Misiunea noastră este să inspirăm și să educăm următoarea generație de inovatori prin programe educaționale, competiții și evenimente de robotică.
            </p>
            <div className="text-center mt-8">
              <Link to="/robotica" className="bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600">Află mai multe</Link>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Echipele Noastre</h2>
          <FeaturedTeams />
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Evenimente Upcoming</h2>
          <UpcomingEvents />
        </div>
      </section>
    </div>
  );
}

